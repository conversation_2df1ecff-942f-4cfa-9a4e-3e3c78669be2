Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-04T11:57:29+07:00"}
{"level":"info","msg":"Using DATABASE_URL for database connection","time":"2025-06-04T11:57:29+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-04T11:57:30+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] GET    /api/v1/shops             --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/search      --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/popular     --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/nearby      --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/filter-options --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopFilterOptions-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/status --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatusBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/slug/:slug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryBySlug-fm (6 handlers)
[GIN-debug] GET    /api/v1/menu/items/:itemId --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (6 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func2 (6 handlers)
{"level":"info","msg":"Customer API starting on port 8900","time":"2025-06-04T11:57:30+07:00"}
{"level":"fatal","msg":"Failed to start server: listen tcp :8900: bind: address already in use","time":"2025-06-04T11:57:30+07:00"}
exit status 1
make[2]: *** [run] Error 1
